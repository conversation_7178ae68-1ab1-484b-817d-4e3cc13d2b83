#include "PlayerContainer.h"
#include <iostream>
using namespace std;

// Constructor
PlayerContainer::PlayerContainer(int initialCapacity) : size(0), capacity(initialCapacity)
{
    players = new Player*[capacity];
    for (int i = 0; i < capacity; ++i)
    {
        players[i] = nullptr;
    }
}

// Rule of Three Implementation

// 1. Destructor
PlayerContainer::~PlayerContainer()
{
    clear();
    delete[] players;
}

// 2. Copy Constructor
PlayerContainer::PlayerContainer(const PlayerContainer &other) : size(0), capacity(0), players(nullptr)
{
    copyFrom(other);
}

// 3. Assignment Operator
PlayerContainer &PlayerContainer::operator=(const PlayerContainer &other)
{
    if (this != &other) // Self-assignment check
    {
        clear();
        delete[] players;
        copyFrom(other);
    }
    return *this;
}

// Member functions
void PlayerContainer::addPlayer(Player *player)
{
    if (size >= capacity)
    {
        resize();
    }
    players[size] = player;
    size++;
}

void PlayerContainer::playGame()
{
    cout << "Starting <PERSON>, <PERSON>, Goose game with " << size << " players!" << endl;
    
    for (int i = 0; i < size; ++i)
    {
        players[i]->makeSound();
        
        // When we reach the Goose, exit after printing
        if (dynamic_cast<Goose*>(players[i]))
        {
            cout << "Game over! Goose found at position " << i << endl;
            break;
        }
    }
}

void PlayerContainer::setGooseAtIndex(int index)
{
    if (index >= 0 && index < size && players[index] != nullptr)
    {
        // Delete the existing player and replace with Goose
        delete players[index];
        players[index] = new Goose();
    }
}

int PlayerContainer::getSize() const
{
    return size;
}

Player *PlayerContainer::getPlayer(int index) const
{
    if (index >= 0 && index < size)
    {
        return players[index];
    }
    return nullptr;
}

void PlayerContainer::clear()
{
    for (int i = 0; i < size; ++i)
    {
        delete players[i];
        players[i] = nullptr;
    }
    size = 0;
}

// Private helper functions
void PlayerContainer::resize()
{
    int newCapacity = capacity * 2;
    Player **newPlayers = new Player*[newCapacity];
    
    // Copy existing players
    for (int i = 0; i < size; ++i)
    {
        newPlayers[i] = players[i];
    }
    
    // Initialize new slots
    for (int i = size; i < newCapacity; ++i)
    {
        newPlayers[i] = nullptr;
    }
    
    delete[] players;
    players = newPlayers;
    capacity = newCapacity;
}

void PlayerContainer::copyFrom(const PlayerContainer &other)
{
    size = other.size;
    capacity = other.capacity;
    players = new Player*[capacity];
    
    for (int i = 0; i < capacity; ++i)
    {
        if (i < size && other.players[i] != nullptr)
        {
            // Deep copy: create new objects based on the type
            if (dynamic_cast<Goose*>(other.players[i]))
            {
                players[i] = new Goose();
            }
            else if (dynamic_cast<Duck*>(other.players[i]))
            {
                players[i] = new Duck();
            }
            else
            {
                players[i] = nullptr;
            }
        }
        else
        {
            players[i] = nullptr;
        }
    }
}
