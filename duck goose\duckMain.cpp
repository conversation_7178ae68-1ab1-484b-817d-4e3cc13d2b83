
#include <iostream>
#include <cstdlib>
#include <ctime>
#include "duck.h"
using namespace std;

int main()
{
    int numPlayers;
    cout << "Enter number of players: ";
    cin >> numPlayers;
    if (numPlayers <= 0)
    {
        cout << "Number of players must be positive." << endl;
        return 1;
    }

    // Generate random number between 0 and numPlayers - 1
    srand(static_cast<unsigned>(time(0)));
    int gooseIndex = rand() % numPlayers;

    // Create dynamically allocated array of Player pointers
    Player **players = new Player *[numPlayers];

    // Set all players except the one at gooseIndex to <PERSON> pointers
    // Set the player at gooseIndex to a Goose pointer
    for (int i = 0; i < numPlayers; ++i)
    {
        if (i == gooseIndex)
            players[i] = new Goose();
        else
            players[i] = new <PERSON>();
    }

    // Display the randomly generated number
    cout << "Goose is at index: " << gooseIndex << endl;

    // Iterate through the array and call the virtual function
    for (int i = 0; i < numPlayers; ++i)
    {
        players[i]->makeSound();

        // When we reach the Goose, exit after printing
        if (dynamic_cast<Goose *>(players[i]))
        {
            break;
        }
    }

    // Clean up all memory before exiting
    for (int i = 0; i < numPlayers; ++i)
        delete players[i];
    delete[] players;

    return 0;
}
