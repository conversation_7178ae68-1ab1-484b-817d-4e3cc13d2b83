
#include <iostream>
#include <cstdlib>
#include <ctime>
#include "duck.h"
using namespace std;

int main()
{
    int numPlayers;
    cout << "Enter number of players: ";
    cin >> numPlayers;
    if (numPlayers <= 0)
    {
        cout << "Number of players must be positive." << endl;
        return 1;
    }

    srand(static_cast<unsigned>(time(0)));
    int gooseIndex = rand() % numPlayers;

    Player **players = new Player *[numPlayers];
    for (int i = 0; i < numPlayers; ++i)
    {
        if (i == gooseIndex)
            players[i] = new Goose(5); // Example radius
        else
            players[i] = new Duck(2, 3); // Example length/width
    }

    cout << "Goose is at index: " << gooseIndex << endl;
    for (int i = 0; i < numPlayers; ++i)
    {
        if (dynamic_cast<Goose *>(players[i]))
        {
            cout << "Goose" << endl;
            break;
        }
        else
        {
            cout << "Duck" << endl;
        }
    }

    // Clean up memory
    for (int i = 0; i < numPlayers; ++i)
        delete players[i];
    delete[] players;

    return 0;
}
