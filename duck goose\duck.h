#include <iostream>
using namespace std;

class Player
{
public:
    virtual int getArea() const = 0;
    virtual ~Player() {} // Virtual destructor for proper cleanup
};

class Goose : public Player
{
private:
    int radius;

public:
    int getArea() const override;
    Goose(int r);
};

class Duck : public Player
{
private:
    int length;
    int width;

public:
    int getArea() const override;
    <PERSON>(int l, int w);
};