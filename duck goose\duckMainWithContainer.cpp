#include <iostream>
#include <cstdlib>
#include <ctime>
#include "duck.h"
#include "PlayerContainer.h"
using namespace std;

void demonstrateRuleOfThree(const PlayerContainer &container)
{
    cout << "\n=== Demonstrating Rule of Three ===" << endl;
    
    // Demonstrate Copy Constructor
    cout << "Creating copy using copy constructor..." << endl;
    PlayerContainer copy1(container);
    cout << "Copy created. Playing game with copy:" << endl;
    copy1.playGame();
    
    // Demonstrate Assignment Operator
    cout << "\nCreating another container and using assignment operator..." << endl;
    PlayerContainer copy2;
    copy2 = container;
    cout << "Assignment completed. Playing game with assigned copy:" << endl;
    copy2.playGame();
    
    cout << "\nRule of Three demonstration complete!" << endl;
    cout << "All copies will be automatically destroyed when function ends," << endl;
    cout << "demonstrating proper destructor behavior." << endl;
}

int main()
{
    int numPlayers;
    cout << "Enter number of players: ";
    cin >> numPlayers;
    if (numPlayers <= 0)
    {
        cout << "Number of players must be positive." << endl;
        return 1;
    }

    // Generate random number between 0 and numPlayers - 1
    srand(static_cast<unsigned>(time(0)));
    int gooseIndex = rand() % numPlayers;

    cout << "Using PlayerContainer class (BONUS implementation)" << endl;
    cout << "Goose will be at index: " << gooseIndex << endl;

    // Create PlayerContainer object
    PlayerContainer gameContainer(numPlayers);
    
    // Add all Duck players first
    for (int i = 0; i < numPlayers; ++i)
    {
        gameContainer.addPlayer(new Duck());
    }
    
    // Set the player at gooseIndex to be a Goose
    gameContainer.setGooseAtIndex(gooseIndex);
    
    // Play the game using the container
    gameContainer.playGame();
    
    // Demonstrate the Rule of Three
    demonstrateRuleOfThree(gameContainer);
    
    cout << "\nProgram ending. PlayerContainer destructor will automatically" << endl;
    cout << "clean up all memory when the object goes out of scope." << endl;

    return 0;
    // PlayerContainer destructor automatically called here, cleaning up all memory
}
